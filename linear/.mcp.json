{"mcpServers": {"aws": {"command": "pnpm", "args": ["--silent", "--prefix", "/home/<USER>/development/mcp/aws-mcp", "start"]}, "github": {"command": "/home/<USER>/development/mcp/github-mcp-server/github-mcp-server", "args": ["stdio"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://lpproduction:<EMAIL>:5432/lp_core_production"]}, "datadog": {"command": "npx", "args": ["datadog-mcp-server", "--a<PERSON><PERSON><PERSON>", "017b56e084f31c5ae50c2d9b888afe44", "--<PERSON><PERSON><PERSON>", "d15ca3e1ef7b8a64832313a1b29deaafe8b1bda2", "--site", "api.datadoghq.com"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "*********************************************************", "SLACK_TEAM_ID": "T03N9LGAH8F"}}, "linear": {"command": "npx", "args": ["-y", "linear-mcp-server"], "env": {"LINEAR_API_KEY": "************************************************"}}}}