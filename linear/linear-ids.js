import axios from 'axios';
import fs from 'fs';

const LINEAR_API_ENDPOINT = 'https://api.linear.app/graphql';
const LINEAR_API_KEY = '************************************************';

const headers = {
  'Authorization': LINEAR_API_KEY,
  'Content-Type': 'application/json'
};

async function fetchInfTeam() {
  const teamQuery = `
    query {
      teams(filter: { key: { eq: "INF" } }) {
        nodes {
          id
          name
          key
          description
        }
      }
    }
  `;

  try {
    const response = await axios.post(LINEAR_API_ENDPOINT, { query: teamQuery }, { headers });
    
    if (response.data.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(response.data.errors)}`);
    }

    return response.data.data.teams.nodes;
  } catch (error) {
    console.error('Error fetching INF team:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

async function fetchInfWorkflowStates() {
  const workflowStatesQuery = `
    query {
      team(id: "50e71128-51a1-45f8-963e-9d35b458a205") {
        id
        name
        key
        states {
          nodes {
            id
            name
            type
            description
            color
            position
          }
        }
      }
    }
  `;

  try {
    const response = await axios.post(LINEAR_API_ENDPOINT, { query: workflowStatesQuery }, { headers });
    
    if (response.data.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(response.data.errors)}`);
    }

    return response.data.data.team.states.nodes;
  } catch (error) {
    console.error('Error fetching INF team workflow states:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

function displayResults(infTeam, workflowStates) {
  console.log('\n=== INF TEAM ===');
  console.log(`Team: ${infTeam.name} (${infTeam.key})`);
  console.log(`ID: ${infTeam.id}`);
  if (infTeam.description) {
    console.log(`Description: ${infTeam.description}`);
  }

  console.log('\n=== INF TEAM WORKFLOW STATES ===');
  console.log(`Found ${workflowStates.length} workflow states:\n`);
  
  workflowStates.forEach((state, index) => {
    console.log(`${index + 1}. ${state.name} (${state.type})`);
    console.log(`   ID: ${state.id}`);
    if (state.description) {
      console.log(`   Description: ${state.description}`);
    }
    if (state.color) {
      console.log(`   Color: ${state.color}`);
    }
    console.log(`   Position: ${state.position}`);
    console.log('');
  });
}

function saveToFile(infTeam, workflowStates) {
  const data = {
    team: infTeam,
    workflowStates: workflowStates,
    fetchedAt: new Date().toISOString()
  };

  try {
    fs.writeFileSync('inf-team-data.json', JSON.stringify(data, null, 2));
    console.log('\n✅ Data saved to inf-team-data.json');
  } catch (error) {
    console.error('Error saving to file:', error.message);
  }
}

async function main() {
  console.log('🔍 Fetching INF team info and workflow states...\n');

  try {
    const [infTeams, workflowStates] = await Promise.all([
      fetchInfTeam(),
      fetchInfWorkflowStates()
    ]);

    if (infTeams.length === 0) {
      console.error('❌ INF team not found');
      process.exit(1);
    }

    const infTeam = infTeams[0];
    displayResults(infTeam, workflowStates);
    saveToFile(infTeam, workflowStates);

    console.log('\n✅ Script completed successfully!');
  } catch (error) {
    console.error('\n❌ Script failed:', error.message);
    process.exit(1);
  }
}

main();